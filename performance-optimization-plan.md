# PasteFlow Performance Optimization Implementation Plan

## Executive Summary
This document outlines a comprehensive plan to dramatically improve the initial loading performance of PasteFlow when opening large codebases and loading saved workspaces. The goal is to achieve instant (<100ms) file tree rendering with progressive enhancement for additional features like token counting and file selection restoration.

## Current Performance Bottlenecks

### 1. Token Counting Blocks Initial Render
- **Issue**: Token counting happens during initial file loading, delaying tree display
- **Location**: `main.js:648-747` (file processing), `src/hooks/use-app-state.ts:680-736` (batch loading)
- **Impact**: 2-5 second delay for large codebases

### 2. Synchronous Tree Building
- **Issue**: Complete tree structure built before any rendering
- **Location**: `src/hooks/use-file-tree.ts:184-374`
- **Impact**: UI freeze during tree construction

### 3. Workspace Loading Inefficiencies
- **Issue**: All workspace data loaded and applied synchronously
- **Location**: `src/hooks/use-workspace-state.ts:58-88`
- **Impact**: Delayed tree visibility when loading saved workspaces

### 4. No Progressive Rendering Strategy
- **Issue**: Tree waits for complete data before showing anything
- **Location**: `src/hooks/use-file-tree.ts:713-721`
- **Impact**: Poor perceived performance

## Optimization Strategy

### Phase 1: Instant Tree Display (Target: <100ms)

#### 1.1 Deferred Token Counting
**Implementation Steps:**
1. Modify `main.js` file processing to skip token counting initially
2. Send file metadata only (name, path, type, size) in first batch
3. Implement background token counting queue
4. Update UI progressively as tokens are counted

**Code Changes:**
- `main.js:processFile()` - Remove initial token counting
- `main.js:sendBatch()` - Add `skipTokenCount` flag
- Create new `TokenCountingQueue` class for background processing
- Add IPC channel `request-token-count-batch` for deferred counting

#### 1.2 Streaming Tree Construction
**Implementation Steps:**
1. Enhance `StreamingTreeBuilder` to emit partial trees immediately
2. Build visible nodes first (expanded directories)
3. Defer building of collapsed directory contents
4. Implement viewport-aware tree building

**Code Changes:**
- `src/utils/streaming-tree-builder.ts` - Add priority queue for visible nodes
- `src/hooks/use-file-tree.ts:199-252` - Modify to render partial trees
- Add `visibilityPriority` calculation based on expanded state

#### 1.3 Optimized IPC Communication
**Implementation Steps:**
1. Implement differential updates instead of full tree replacements
2. Use binary encoding for large file lists
3. Batch IPC messages with adaptive sizing
4. Add compression for large payloads

**Code Changes:**
- Create `IPCOptimizer` class with message queuing
- Implement delta encoding for file list updates
- Add LZ4 compression for payloads > 1MB

### Phase 2: Smart Workspace Loading

#### 2.1 Two-Stage Workspace Loading
**Implementation Steps:**
1. Load and render file tree structure first
2. Apply selections and prompts after tree is visible
3. Load token counts from cached database values
4. Implement smooth visual transitions

**Code Changes:**
- `src/hooks/use-workspace-state.ts:58-88` - Split loading into stages
- Add `WorkspaceLoadingStage` enum
- Implement `requestAnimationFrame` based transitions
- Cache token counts in SQLite database

#### 2.2 Persistent Token Cache
**Implementation Steps:**
1. Store token counts in SQLite with file hash
2. Validate cache using file modification time
3. Background refresh of stale cache entries
4. Precompute tokens for common file types

**Database Schema:**
```sql
CREATE TABLE token_cache (
  file_path TEXT PRIMARY KEY,
  file_hash TEXT NOT NULL,
  token_count INTEGER NOT NULL,
  last_modified INTEGER NOT NULL,
  created_at INTEGER NOT NULL
);
CREATE INDEX idx_token_cache_modified ON token_cache(last_modified);
```

#### 2.3 Progressive Selection Restoration
**Implementation Steps:**
1. Show file tree immediately with loading indicators
2. Apply selections as files become available
3. Animate selection transitions
4. Prioritize loading of selected files

**Code Changes:**
- Add `SelectionLoader` component with transition animations
- Implement priority queue for selected file loading
- Add visual loading states for pending selections

### Phase 3: Advanced Optimizations

#### 3.1 Virtual Scrolling for File Tree
**Implementation Steps:**
1. Implement windowing for trees > 1000 nodes
2. Render only visible nodes + buffer
3. Maintain scroll position during updates
4. Add intersection observer for lazy loading

**Code Changes:**
- Create `VirtualizedTree` component using react-window
- Implement custom row height calculator
- Add scroll position restoration logic

#### 3.2 Web Worker Token Counting
**Implementation Steps:**
1. Move all token counting to dedicated workers
2. Implement worker pool management
3. Add priority queue for user-initiated counts
4. Cache results aggressively

**Code Changes:**
- Enhance `TokenWorkerPool` with better scheduling
- Add `WorkerPriorityQueue` with preemption
- Implement result caching layer

#### 3.3 Predictive Preloading
**Implementation Steps:**
1. Preload likely-to-be-selected files
2. Use ML model for selection prediction
3. Background load neighboring files
4. Implement smart prefetching strategy

**Code Changes:**
- Add `PredictiveLoader` service
- Implement selection pattern analysis
- Create prefetch queue with limits

## Implementation Timeline

### Week 1: Foundation (Critical Path)
- [ ] Day 1-2: Implement deferred token counting in main.js
- [ ] Day 3-4: Create two-stage workspace loading
- [ ] Day 5: Add progress indicators and loading states

### Week 2: Core Optimizations
- [ ] Day 1-2: Implement streaming tree construction
- [ ] Day 3-4: Add persistent token cache
- [ ] Day 5: Optimize IPC communication

### Week 3: Progressive Enhancement
- [ ] Day 1-2: Add virtual scrolling for large trees
- [ ] Day 3-4: Implement progressive selection restoration
- [ ] Day 5: Add smooth transition animations

### Week 4: Polish & Testing
- [ ] Day 1-2: Performance testing and benchmarking
- [ ] Day 3-4: Bug fixes and edge case handling
- [ ] Day 5: Documentation and release preparation

## Performance Targets

### Initial Load (Empty State)
- **Current**: 3-5 seconds for 1500 files
- **Target**: <100ms for tree structure, <500ms full functionality
- **Measurement**: Time to first meaningful paint

### Workspace Load (With Selections)
- **Current**: 2-4 seconds
- **Target**: <200ms for tree, <1s for complete restoration
- **Measurement**: Time to interactive

### Token Counting
- **Current**: Blocks UI for 2-3 seconds
- **Target**: Background processing, no UI blocking
- **Measurement**: Main thread blocking time

## Risk Mitigation

### Risk 1: Breaking Changes
- **Mitigation**: Feature flags for gradual rollout
- **Testing**: Comprehensive test suite for each phase
- **Rollback**: Git tags for each stable version

### Risk 2: Memory Leaks
- **Mitigation**: Implement proper cleanup in all components
- **Testing**: Memory profiling with Chrome DevTools
- **Monitoring**: Add memory usage tracking

### Risk 3: Race Conditions
- **Mitigation**: Implement proper state synchronization
- **Testing**: Stress testing with rapid operations
- **Design**: Use single source of truth pattern

## Testing Strategy

### Unit Tests
- Test each optimization in isolation
- Mock heavy operations for fast tests
- Achieve 80% code coverage minimum

### Integration Tests
- Test complete workflows end-to-end
- Verify state consistency
- Test error recovery paths

### Performance Tests
- Benchmark each optimization
- Create performance regression tests
- Monitor key metrics in CI/CD

### User Acceptance Tests
- Test with real-world large codebases
- Gather user feedback on perceived performance
- A/B testing for optimization effectiveness

## Monitoring & Metrics

### Key Performance Indicators
1. Time to First Tree Node (TTFN)
2. Time to Interactive (TTI)
3. Main Thread Blocking Time
4. Memory Usage Peak
5. IPC Message Throughput

### Telemetry Implementation
```typescript
interface PerformanceMetrics {
  ttfn: number;           // Time to first node
  tti: number;            // Time to interactive
  treeNodes: number;      // Total nodes rendered
  tokenCountTime: number; // Time for token counting
  memoryPeak: number;     // Peak memory usage
}
```

## Success Criteria

### Must Have
- [ ] File tree renders in <100ms for 1500+ files
- [ ] No UI blocking during token counting
- [ ] Smooth workspace loading with progressive enhancement
- [ ] No performance regressions in existing features

### Should Have
- [ ] Virtual scrolling for trees >1000 nodes
- [ ] Token count caching persistence
- [ ] Predictive file preloading
- [ ] Sub-50ms response for all user interactions

### Nice to Have
- [ ] ML-based selection prediction
- [ ] Advanced prefetching strategies
- [ ] Real-time collaborative features
- [ ] Cloud-based token counting service

## Conclusion

This optimization plan addresses the core performance issues in PasteFlow through a phased approach that prioritizes immediate user-visible improvements while laying the groundwork for advanced optimizations. The key insight is that users need to see the file tree immediately - everything else can be progressively enhanced.

By implementing deferred token counting, streaming tree construction, and two-stage workspace loading, we can achieve a 10-50x improvement in perceived performance while maintaining all existing functionality. The plan is designed to be implemented incrementally with minimal risk and maximum impact on user experience.