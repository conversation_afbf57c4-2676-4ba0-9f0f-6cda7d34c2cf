import { useCallback, useState, useRef, useEffect } from 'react';
import { useTokenCounter } from './use-token-counter';
import { estimateTokenCount } from '../utils/token-utils';
import { FileData } from '../types/file-types';

export enum TokenPriority {
  IMMEDIATE = 0,    // File just selected or clipboard operation
  USER_HOVER = 3,   // User hovering over item
  DIRECTORY_AGG = 5, // Directory aggregation
  BACKGROUND = 8,   // Background pre-calculation
  PREFETCH = 10     // Speculative prefetch
}

interface UseTokenCountingOnDemandReturn {
  requestTokenCount: (filePath: string, priority?: number, immediate?: boolean) => Promise<number | undefined>;
  handleFileHover: (filePath: string) => void;
  handleFileUnhover: (filePath: string) => void;
  handleDirectoryHover: (directoryPath: string, childFiles: FileData[]) => void;
  pendingRequests: Set<string>;
  tokenCounts: Map<string, number>;
  isCalculating: (filePath: string) => boolean;
  clearHoverTimeouts: () => void;
}

export const useTokenCountingOnDemand = (
  _allFiles: FileData[],  // Prefixed with underscore as it's currently unused
  updateFileTokenCount: (filePath: string, tokenCount: number) => void,
  getFileContent: (filePath: string) => Promise<string | undefined>
): UseTokenCountingOnDemandReturn => {
  const [pendingRequests, setPendingRequests] = useState<Set<string>>(new Set());
  const [tokenCounts, setTokenCounts] = useState<Map<string, number>>(new Map());
  const hoverTimeouts = useRef<Map<string, NodeJS.Timeout>>(new Map());
  const { countTokens: workerCountTokens, isReady: isTokenWorkerReady } = useTokenCounter();

  // Clean up timeouts on unmount
  useEffect(() => {
    return () => {
      hoverTimeouts.current.forEach(timeout => clearTimeout(timeout));
      hoverTimeouts.current.clear();
    };
  }, []);

  const requestTokenCount = useCallback(async (
    filePath: string,
    priority: number = TokenPriority.BACKGROUND,
    _immediate: boolean = false  // Prefixed with underscore as it's currently unused
  ): Promise<number | undefined> => {
    // Check if already calculated
    const existingCount = tokenCounts.get(filePath);
    if (existingCount !== undefined) {
      return existingCount;
    }

    // Check if already pending
    if (pendingRequests.has(filePath)) {
      // Wait for existing request to complete
      return new Promise((resolve) => {
        const checkInterval = setInterval(() => {
          if (!pendingRequests.has(filePath)) {
            clearInterval(checkInterval);
            resolve(tokenCounts.get(filePath));
          }
        }, 100);
      });
    }

    // Mark as pending
    setPendingRequests(prev => new Set(prev).add(filePath));

    try {
      // Get file content
      const content = await getFileContent(filePath);
      if (!content) {
        setPendingRequests(prev => {
          const next = new Set(prev);
          next.delete(filePath);
          return next;
        });
        return undefined;
      }

      // Calculate tokens
      let tokenCount: number;
      if (isTokenWorkerReady) {
        tokenCount = await workerCountTokens(content, priority);
      } else {
        tokenCount = estimateTokenCount(content);
      }

      // Update state
      setTokenCounts(prev => new Map(prev).set(filePath, tokenCount));
      updateFileTokenCount(filePath, tokenCount);

      // Clear pending
      setPendingRequests(prev => {
        const next = new Set(prev);
        next.delete(filePath);
        return next;
      });

      return tokenCount;
    } catch (error) {
      console.error(`Error counting tokens for ${filePath}:`, error);
      setPendingRequests(prev => {
        const next = new Set(prev);
        next.delete(filePath);
        return next;
      });
      return undefined;
    }
  }, [pendingRequests, tokenCounts, getFileContent, isTokenWorkerReady, workerCountTokens, updateFileTokenCount]);

  const handleFileHover = useCallback((filePath: string) => {
    // Cancel existing timeout if any
    const existingTimeout = hoverTimeouts.current.get(filePath);
    if (existingTimeout) {
      clearTimeout(existingTimeout);
    }

    // Set new timeout for debounced hover
    const timeout = setTimeout(() => {
      requestTokenCount(filePath, TokenPriority.USER_HOVER, false);
      hoverTimeouts.current.delete(filePath);
    }, 300);

    hoverTimeouts.current.set(filePath, timeout);
  }, [requestTokenCount]);

  const handleFileUnhover = useCallback((filePath: string) => {
    // Cancel hover timeout if user moves away quickly
    const existingTimeout = hoverTimeouts.current.get(filePath);
    if (existingTimeout) {
      clearTimeout(existingTimeout);
      hoverTimeouts.current.delete(filePath);
    }
  }, []);

  const handleDirectoryHover = useCallback(async (directoryPath: string, childFiles: FileData[]) => {
    // Calculate total tokens for all files in directory
    const directoryFiles = childFiles.filter(f => 
      f.path.startsWith(directoryPath) && !f.isDirectory
    );

    // Request token counts for all child files with directory priority
    const promises = directoryFiles.map(file => 
      requestTokenCount(file.path, TokenPriority.DIRECTORY_AGG, false)
    );

    await Promise.all(promises);
  }, [requestTokenCount]);

  const isCalculating = useCallback((filePath: string): boolean => {
    return pendingRequests.has(filePath);
  }, [pendingRequests]);

  const clearHoverTimeouts = useCallback(() => {
    hoverTimeouts.current.forEach(timeout => clearTimeout(timeout));
    hoverTimeouts.current.clear();
  }, []);

  return {
    requestTokenCount,
    handleFileHover,
    handleFileUnhover,
    handleDirectoryHover,
    pendingRequests,
    tokenCounts,
    isCalculating,
    clearHoverTimeouts
  };
};