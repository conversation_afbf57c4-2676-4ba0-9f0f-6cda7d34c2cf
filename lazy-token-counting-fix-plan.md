# Lazy Token Counting Fix Implementation Plan

## Problem Summary
The lazy token counting implementation is NOT working because there's automatic eager token counting happening in `main.js` that queues ALL files for token counting immediately after directory scanning completes. This happens for EVERY folder opened, not just workspaces.

## Root Cause
**Location**: `main.js` lines 1014-1025

After directory scanning completes, the code automatically:
1. Filters all non-binary, non-skipped files
2. Queues them ALL for token counting with priority 2
3. This happens with a 500ms delay after the file tree loads
4. Token counts are then sent back via IPC and update ALL files

This completely bypasses the lazy loading mechanism since files get their token counts before users can even interact with them.

## Why Previous Analysis Was Wrong
The initial analysis focused on workspace persistence, but testing with new folders proved this wasn't the issue. The real problem is that EVERY folder load triggers automatic token counting for ALL files, regardless of whether it's a saved workspace or a new folder.

## Implementation Plan

### Phase 1: Remove Automatic Token Counting
**Goal**: Stop automatic token counting after directory scan

#### 1.1 Remove Auto-Queue Logic (`main.js`)
**Lines 1014-1025**: Delete the entire setTimeout block that queues files:
```javascript
// DELETE THIS ENTIRE BLOCK:
// Queue all files for token counting AFTER tree is fully loaded and displayed
// This happens completely in the background
setTimeout(() => {
  const filesToCount = allFiles
    .filter(f => !f.isDirectory && !f.isBinary && !f.isSkipped)
    .map(f => ({ path: f.path }));
  
  if (filesToCount.length > 0) {
    // Queue with low priority for background processing
    tokenCountingQueue.enqueue(filesToCount, 2);
  }
}, 500); // Small delay to ensure UI has rendered
```

### Phase 2: Verify Lazy Loading Works
**Goal**: Ensure the existing lazy loading mechanism functions properly

#### 2.1 Verify Hover Triggers (`src/components/tree-item.tsx`)
- Line 655-660: Hover handler should trigger for files without token counts
- Confirm `handleFileHover` is properly passed from parent components
- Ensure debouncing works (300ms delay)

#### 2.2 Verify Selection Triggers
- Check that file selection still triggers token counting
- Verify directory selection aggregates tokens properly

#### 2.3 Test Token Count Display
- Files should show no token count initially
- On hover: "Calculating..." should appear
- After calculation: Token count should display

### Phase 3: Remove Workspace Token Persistence (Optional)
**Goal**: Ensure fresh token calculation even for saved workspaces

#### 3.1 Update Workspace Save (`src/hooks/use-app-state.ts`)
- **Line 813-822**: Exclude `tokenCount` from saved files:
  ```typescript
  const allFilesWithoutContent = allFiles.map(file => {
    const { content, tokenCount, ...fileWithoutContent } = file;
    return fileWithoutContent;
  });
  ```

- **Line 828-836**: Remove the `tokenCounts` property from workspace state

#### 3.2 Remove Batch Token Request on Workspace Load
- **Line 1054-1065**: Remove batch token counting for selected files:
  ```typescript
  // DELETE THIS BLOCK
  ```

### Phase 4: Optimize Token Counting Triggers
**Goal**: Only calculate tokens when truly needed

#### 4.1 Smart Prefetching
- Implement viewport-based prefetching
- Calculate tokens for visible files in background
- Use idle time for nearby files

#### 4.2 Priority Adjustments
- Selected files: IMMEDIATE (priority 0)
- Hovered files: USER_HOVER (priority 3)
- Visible files: BACKGROUND (priority 8)
- Off-screen files: Don't calculate

### Phase 5: Testing Checklist
**Goal**: Verify the fix works correctly

#### 5.1 New Folder Test
- [ ] Open a new folder never opened before
- [ ] Verify NO token counts appear initially
- [ ] Hover over a file - see "Calculating..."
- [ ] Token count appears after calculation
- [ ] Other files still show no token counts

#### 5.2 Large Folder Test
- [ ] Open folder with 1000+ files
- [ ] Verify fast initial load (no token calculation delay)
- [ ] Scroll through list - no automatic token calculation
- [ ] Only hovered/selected files get token counts

#### 5.3 Workspace Test
- [ ] Save workspace after removing persistence
- [ ] Load workspace - no token counts should appear
- [ ] Verify lazy loading still works after workspace load

#### 5.4 Performance Test
- [ ] Measure initial folder load time (should be 70-90% faster)
- [ ] Verify smooth scrolling
- [ ] Check CPU usage is minimal when idle

## Implementation Order
1. **FIRST**: Remove automatic token counting (Phase 1) - This alone should fix the issue
2. **TEST**: Verify lazy loading works (Phase 2)
3. **OPTIONAL**: Remove workspace persistence if needed (Phase 3)
4. **OPTIMIZE**: Add smart prefetching (Phase 4)
5. **VALIDATE**: Run all tests (Phase 5)

## Key Code Locations
- **Main problem**: `main.js:1014-1025` - Auto-queue after directory scan
- **Lazy loading hook**: `src/hooks/use-token-counting-on-demand.ts`
- **Hover trigger**: `src/components/tree-item.tsx:655-660`
- **Token display**: `src/components/tree-item.tsx:287-299`
- **Workspace persistence**: `src/hooks/use-app-state.ts:813-836,1054-1065`

## Success Criteria
✅ Opening a new folder shows NO token counts initially
✅ Token counts only appear after user interaction (hover/select)
✅ "Calculating..." state is visible during token calculation
✅ Initial folder load time improved by 70%+
✅ CPU usage minimal when idle
✅ Memory usage reasonable for large folders

## Risk Assessment
- **Low Risk**: Removing auto-queue won't break functionality
- **Medium Risk**: Users might expect immediate token counts
- **Mitigation**: Could add preference for eager vs lazy loading

## Rollback Plan
If issues arise, the automatic token counting can be restored by:
1. Re-adding the setTimeout block in `main.js`
2. Adjusting priority from 2 to lower value for background processing

## Timeline
- Phase 1: 5 minutes (delete code block)
- Phase 2: 15 minutes (verify existing functionality)
- Phase 3: 20 minutes (optional - workspace changes)
- Phase 4: 30 minutes (optimization)
- Phase 5: 30 minutes (testing)
- **Total**: ~30 minutes for basic fix, 1.5 hours for full implementation

## Summary
The fix is surprisingly simple - just remove the automatic token counting that happens after directory scanning. The lazy loading infrastructure is already in place and working, it's just being bypassed by eager token calculation that defeats its purpose.