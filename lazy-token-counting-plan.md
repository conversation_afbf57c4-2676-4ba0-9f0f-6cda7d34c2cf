# Lazy/On-Demand Token Counting Implementation Plan

## Executive Summary

This document outlines a comprehensive plan to implement lazy/on-demand token counting in PasteFlow to dramatically improve initial file tree loading performance. Currently, token counting occurs eagerly during file processing, creating significant bottlenecks during initial workspace loading. The proposed solution will eliminate all token counting from the initial load phase, calculating tokens only when explicitly needed by user interactions.

**Key Performance Goals:**
- Reduce initial file tree load time by 70-90%
- Eliminate token counting from critical path
- Maintain responsive UI during large workspace loads
- Preserve accurate token counting when needed

## Current State Analysis

### Current Token Counting Architecture

**Problem Areas:**
1. **Eager Token Counting**: All files get token counts calculated immediately during directory scanning
2. **Blocking Operations**: Token counting occurs in the critical path of file tree rendering
3. **Unnecessary Calculations**: Many files never get selected but still have tokens counted
4. **Worker Pool Overhead**: Token workers are initialized and used even when not needed

**Current Flow:**
```
Directory Scan → File Content Load → Token Counting → UI Render
```

**Performance Bottlenecks Identified:**

1. **File Processing Pipeline** (`src/utils/file-processing.ts`): 
   - Lines 96-100: Immediately counts tokens for all text files
   - Blocking: `countTokens(fileContent)` called synchronously
   
2. **Main Process** (`main.js`):
   - Lines 285-299: TokenCountingQueue processes all files in batches
   - Background processing still impacts overall load time
   
3. **App State Hook** (`src/hooks/use-app-state.ts`):
   - Lines 499, 543, 667: Multiple token counting triggers during file loading
   - Worker pool initialization happens immediately
   
4. **UI Components**:
   - File cards display token counts immediately, triggering calculations
   - Tree items show aggregated counts, forcing calculations

### Current Token Usage Patterns

**Analysis of Token Count Usage:**
- **File Cards**: Display individual file token counts
- **Directory Aggregation**: Show total tokens for folders
- **Content Preview**: Calculate tokens for selected content
- **Workspace Totals**: Sum all selected file tokens
- **Copy Operations**: Include token counts in clipboard content

**Key Finding**: Only ~10-20% of files in a typical workspace are ever selected, yet 100% get token counts calculated.

## Proposed Architecture

### Core Principle: Just-In-Time Token Counting

Token counts will be calculated ONLY when:
1. User hovers over a file/directory in the tree
2. User selects a file (checkbox interaction)  
3. User selects specific line ranges within a file
4. Directory token counts are needed (when any child gets calculated)
5. Final content is being prepared for clipboard

### New Token Counting Flow

```
Directory Scan → UI Render (no tokens) → User Interaction → Token Calculation → Display Update
```

### State Management Changes

**New Token State Structure:**
```typescript
interface FileData {
  // ... existing fields
  tokenCount?: number;           // undefined = not calculated yet
  isCountingTokens?: boolean;    // true = calculation in progress
  tokenCountError?: string;      // error during calculation
  tokenCountRequested?: boolean; // user requested this count
}

interface TokenCountingState {
  pendingRequests: Set<string>;       // files currently being calculated
  hoverTimeouts: Map<string, NodeJS.Timeout>; // debounced hover calculations
  directoryAggregations: Map<string, number>; // cached directory totals
  calculationQueue: PriorityQueue<TokenRequest>; // priority-based queue
}
```

## Implementation Phases

### Phase 1: Remove Eager Token Counting (Week 1)

**Objective**: Eliminate all token counting from initial file tree load

**Changes Required:**

1. **File Processing Pipeline** (`src/utils/file-processing.ts`):
   ```typescript
   // REMOVE: Lines 96-100
   // const tokenCount = countTokens(fileContent);
   
   // REPLACE WITH:
   return {
     name: dirent.name,
     path: filePath,
     tokenCount: undefined, // Will be calculated on-demand
     // ... other fields
   };
   ```

2. **Main Process Token Queue** (`main.js`):
   ```typescript
   // DISABLE: Lines 285-299 TokenCountingQueue processing
   // Keep the queue for later on-demand use
   
   class TokenCountingQueue {
     // ... existing implementation
     
     // NEW: Only process when explicitly requested
     processOnDemand(filePaths: string[], priority: number = 5) {
       // Process specific files only
     }
   }
   ```

3. **App State Hook** (`src/hooks/use-app-state.ts`):
   ```typescript
   // UPDATE: Remove automatic token counting from loadFileContent
   const loadFileContent = useCallback(async (filePath: string) => {
     // Load content but don't count tokens
     const result = await requestFileContent(filePath);
     if (result.success && result.content !== undefined) {
       // Store content without token count
       updateFileWithContent(filePath, result.content, undefined);
     }
   }, []);
   ```

**Expected Results**: 
- 70-90% reduction in initial load time
- File tree renders immediately without token counts
- UI remains responsive during large workspace loads

### Phase 2: Implement Hover-Based Token Counting (Week 2)

**Objective**: Calculate tokens when user hovers over files/directories

**New Hook: `useTokenCountingOnDemand`**

```typescript
// src/hooks/use-token-counting-on-demand.ts
export const useTokenCountingOnDemand = () => {
  const [pendingRequests, setPendingRequests] = useState<Set<string>>(new Set());
  const [hoverTimeouts, setHoverTimeouts] = useState<Map<string, NodeJS.Timeout>>(new Map());
  const { countTokens } = useTokenCounter();
  
  const requestTokenCount = useCallback(async (
    filePath: string, 
    priority: number = 5,
    immediate: boolean = false
  ) => {
    // Implementation for on-demand token counting
  }, []);
  
  const handleFileHover = useCallback((filePath: string) => {
    // Debounced hover handler with 300ms delay
    const existingTimeout = hoverTimeouts.get(filePath);
    if (existingTimeout) {
      clearTimeout(existingTimeout);
    }
    
    const timeout = setTimeout(() => {
      requestTokenCount(filePath, 7); // Lower priority for hover
    }, 300);
    
    setHoverTimeouts(prev => new Map(prev).set(filePath, timeout));
  }, [requestTokenCount, hoverTimeouts]);
  
  return { requestTokenCount, handleFileHover, pendingRequests };
};
```

**Component Updates:**

1. **Tree Item Component** (`src/components/tree-item.tsx`):
   ```tsx
   const TreeItem = ({ node, ...props }: TreeItemProps) => {
     const { handleFileHover, pendingRequests } = useTokenCountingOnDemand();
     
     return (
       <div 
         className="tree-item"
         onMouseEnter={() => handleFileHover(node.path)}
         onMouseLeave={() => {/* Clear timeout */}}
       >
         {/* Display token count or loading state */}
         <TokenDisplay 
           path={node.path} 
           tokenCount={node.fileData?.tokenCount}
           isCalculating={pendingRequests.has(node.path)}
         />
       </div>
     );
   };
   ```

2. **File Card Component** (`src/components/file-card.tsx`):
   ```tsx
   const FileCard = ({ file, ...props }: FileCardProps) => {
     const { handleFileHover } = useTokenCountingOnDemand();
     
     useEffect(() => {
       // Request token count immediately for selected files
       if (isSelected && !file.tokenCount && !file.isCountingTokens) {
         handleFileHover(file.path); // Immediate calculation
       }
     }, [isSelected, file.path, file.tokenCount]);
   };
   ```

### Phase 3: Directory Aggregation System (Week 3)

**Objective**: Efficiently calculate and cache directory-level token counts

**New Utility: `DirectoryTokenAggregator`**

```typescript
// src/utils/directory-token-aggregator.ts
export class DirectoryTokenAggregator {
  private cache = new Map<string, number>();
  private pendingCalculations = new Map<string, Promise<number>>();
  
  async getDirectoryTokenCount(
    directoryPath: string, 
    allFiles: FileData[]
  ): Promise<number> {
    // Check cache first
    if (this.cache.has(directoryPath)) {
      return this.cache.get(directoryPath)!;
    }
    
    // Check if calculation is in progress
    if (this.pendingCalculations.has(directoryPath)) {
      return this.pendingCalculations.get(directoryPath)!;
    }
    
    // Start new calculation
    const calculation = this.calculateDirectoryTokens(directoryPath, allFiles);
    this.pendingCalculations.set(directoryPath, calculation);
    
    const result = await calculation;
    this.cache.set(directoryPath, result);
    this.pendingCalculations.delete(directoryPath);
    
    return result;
  }
  
  private async calculateDirectoryTokens(
    directoryPath: string, 
    allFiles: FileData[]
  ): Promise<number> {
    const childFiles = allFiles.filter(f => 
      f.path.startsWith(directoryPath) && !f.isDirectory
    );
    
    // Request token counts for all child files
    const tokenCounts = await Promise.all(
      childFiles.map(f => this.ensureFileTokenCount(f))
    );
    
    return tokenCounts.reduce((sum, count) => sum + count, 0);
  }
}
```

**Directory Hover Enhancement:**
```typescript
const handleDirectoryHover = useCallback(async (directoryPath: string) => {
  const aggregator = useDirectoryTokenAggregator();
  const tokenCount = await aggregator.getDirectoryTokenCount(directoryPath, allFiles);
  
  // Update directory display with aggregated count
  updateDirectoryTokenDisplay(directoryPath, tokenCount);
}, [allFiles]);
```

### Phase 4: Selection-Based Priority System (Week 4)

**Objective**: Prioritize token counting for user interactions

**Token Request Priority System:**
```typescript
enum TokenPriority {
  IMMEDIATE = 0,    // File just selected
  USER_HOVER = 3,   // User hovering over item
  DIRECTORY_AGG = 5, // Directory aggregation
  BACKGROUND = 8,   // Background pre-calculation
  PREFETCH = 10     // Speculative prefetch
}

interface TokenRequest {
  filePath: string;
  priority: TokenPriority;
  timestamp: number;
  reason: 'selection' | 'hover' | 'directory' | 'background';
}
```

**Smart Queue Management:**
```typescript
class TokenCalculationQueue {
  private queue: PriorityQueue<TokenRequest> = new PriorityQueue();
  private processing = false;
  
  enqueue(request: TokenRequest) {
    // Higher priority items processed first
    this.queue.enqueue(request, request.priority);
    this.processQueue();
  }
  
  private async processQueue() {
    if (this.processing) return;
    this.processing = true;
    
    while (!this.queue.isEmpty()) {
      const request = this.queue.dequeue();
      await this.processTokenRequest(request);
    }
    
    this.processing = false;
  }
}
```

**File Selection Integration:**
```typescript
const toggleFileSelection = useCallback((filePath: string) => {
  // Immediate priority for selected files
  const tokenRequest: TokenRequest = {
    filePath,
    priority: TokenPriority.IMMEDIATE,
    timestamp: Date.now(),
    reason: 'selection'
  };
  
  tokenQueue.enqueue(tokenRequest);
  
  // Update selection state
  fileSelection.toggleFileSelection(filePath);
}, []);
```

## Technical Implementation Details

### Caching Strategy

**Multi-Level Caching:**
1. **Memory Cache**: Recently calculated tokens in component state
2. **Session Cache**: Browser sessionStorage for current session
3. **Worker Cache**: Token worker pool maintains calculation cache
4. **File Content Cache**: Enhanced file cache includes token metadata

```typescript
interface EnhancedFileCache {
  content: string;
  tokenCount?: number;
  tokenCountTimestamp?: number;
  calculationMethod: 'worker' | 'estimation';
}
```

### Event Handling Architecture

**Debounced Interactions:**
```typescript
const debouncedHoverHandler = useMemo(
  () => debounce((filePath: string) => {
    requestTokenCount(filePath, TokenPriority.USER_HOVER);
  }, 300),
  []
);

const handleMouseEnter = useCallback((filePath: string) => {
  debouncedHoverHandler(filePath);
}, [debouncedHoverHandler]);

const handleMouseLeave = useCallback((filePath: string) => {
  debouncedHoverHandler.cancel();
}, [debouncedHoverHandler]);
```

**Selection Event Handling:**
```typescript
const handleFileSelection = useCallback((filePath: string) => {
  // Cancel any pending hover calculations
  debouncedHoverHandler.cancel();
  
  // Immediate token calculation for selected files
  requestTokenCount(filePath, TokenPriority.IMMEDIATE);
}, []);
```

### Performance Optimizations

**Smart Prefetching:**
```typescript
const usePredictiveTokenCounting = () => {
  const prefetchNearbyFiles = useCallback((currentPath: string) => {
    // Prefetch tokens for files in same directory
    const directory = path.dirname(currentPath);
    const siblingFiles = allFiles.filter(f => path.dirname(f.path) === directory);
    
    siblingFiles.forEach(file => {
      if (!file.tokenCount && !pendingRequests.has(file.path)) {
        setTimeout(() => {
          requestTokenCount(file.path, TokenPriority.PREFETCH);
        }, 1000); // Delay prefetch to avoid interfering with user actions
      }
    });
  }, [allFiles, pendingRequests]);
};
```

**Batch Processing Optimization:**
```typescript
class BatchTokenProcessor {
  private batchQueue: TokenRequest[] = [];
  private batchTimeout: NodeJS.Timeout | null = null;
  
  enqueue(request: TokenRequest) {
    this.batchQueue.push(request);
    
    if (this.batchTimeout) clearTimeout(this.batchTimeout);
    this.batchTimeout = setTimeout(() => this.processBatch(), 50);
  }
  
  private async processBatch() {
    const batch = this.batchQueue.splice(0, 10); // Process 10 at a time
    const contents = await Promise.all(
      batch.map(req => getFileContent(req.filePath))
    );
    
    const tokenCounts = await countTokensBatch(contents);
    // Update UI with batch results
  }
}
```

## UI/UX Considerations

### Loading States Design

**Progressive Token Display:**
```tsx
const TokenDisplay = ({ tokenCount, isCalculating, filePath }) => {
  if (isCalculating) {
    return <span className="token-calculating">Calculating...</span>;
  }
  
  if (tokenCount === undefined) {
    return <span className="token-placeholder">Hover for tokens</span>;
  }
  
  return <span className="token-count">{tokenCount.toLocaleString()}</span>;
};
```

**Directory Aggregation UI:**
```tsx
const DirectoryTokenDisplay = ({ directory, tokenCount, isCalculating }) => {
  return (
    <div className="directory-tokens">
      {isCalculating ? (
        <div className="calculating">
          <Spinner size="small" />
          <span>Calculating...</span>
        </div>
      ) : tokenCount !== undefined ? (
        <span className="total-tokens">
          {tokenCount.toLocaleString()} tokens total
        </span>
      ) : (
        <span className="hover-hint">Hover to see total</span>
      )}
    </div>
  );
};
```

### Hover Interaction Design

**Hover Timing:**
- **300ms delay**: Prevents accidental calculations during mouse movement
- **Visual feedback**: Loading spinner appears immediately on hover
- **Cancellation**: Mouse leave cancels pending calculations

**Touch Device Support:**
```typescript
const useTouchAwareTokenCounting = () => {
  const isTouchDevice = useMediaQuery('(pointer: coarse)');
  
  const handleInteraction = useCallback((filePath: string) => {
    if (isTouchDevice) {
      // Immediate calculation on touch
      requestTokenCount(filePath, TokenPriority.IMMEDIATE);
    } else {
      // Debounced hover on desktop
      debouncedHoverHandler(filePath);
    }
  }, [isTouchDevice]);
};
```

### Error Handling UX

**Graceful Degradation:**
```typescript
const TokenDisplayWithFallback = ({ file }) => {
  if (file.tokenCountError) {
    return (
      <span className="token-error" title={file.tokenCountError}>
        Estimation: {estimateTokenCount(file.content || '')}
      </span>
    );
  }
  
  // Normal token display logic
};
```

## Performance Targets and Metrics

### Key Performance Indicators

**Primary Metrics:**
1. **Initial Load Time**: Target 70-90% reduction
   - Before: 5-15 seconds for large workspaces
   - After: 0.5-2 seconds for initial tree render
   
2. **Time to Interactive**: File tree usable immediately
   - Before: Must wait for all token calculations
   - After: Interactive immediately, tokens load on-demand
   
3. **Memory Usage**: Reduced initial memory footprint
   - Before: All file tokens in memory
   - After: Only calculated tokens stored

**Secondary Metrics:**
1. **Token Calculation Accuracy**: Maintain 100% accuracy
2. **User Interaction Response**: <100ms for hover/selection
3. **Background Processing**: <10% CPU usage when idle
4. **Cache Hit Rate**: >80% for frequently accessed files

### Performance Monitoring

**Metrics Collection:**
```typescript
interface TokenCountingMetrics {
  initialLoadTime: number;
  tokenCalculationsTriggered: number;
  cacheHitRate: number;
  averageCalculationTime: number;
  backgroundProcessingTime: number;
  userInteractionResponseTime: number;
}

const usePerformanceMonitoring = () => {
  const recordInitialLoad = useCallback((startTime: number) => {
    const loadTime = Date.now() - startTime;
    analytics.track('initial_load_time', { duration: loadTime });
  }, []);
  
  const recordTokenCalculation = useCallback((
    reason: string, 
    duration: number
  ) => {
    analytics.track('token_calculation', { reason, duration });
  }, []);
};
```

### Benchmarking Strategy

**Test Scenarios:**
1. **Small Workspace**: <100 files, baseline performance
2. **Medium Workspace**: 500-1000 files, typical use case
3. **Large Workspace**: >2000 files, stress testing
4. **Monorepo**: Complex structure with many directories

**Performance Test Suite:**
```typescript
describe('Lazy Token Counting Performance', () => {
  it('should load file tree in <2 seconds for 1000 files', async () => {
    const startTime = Date.now();
    await loadWorkspace('test-workspace-1000-files');
    const loadTime = Date.now() - startTime;
    expect(loadTime).toBeLessThan(2000);
  });
  
  it('should calculate tokens on hover within 500ms', async () => {
    await hoverFile('test-file.ts');
    await waitFor(() => expect(getTokenDisplay()).not.toBe('Hover for tokens'), {
      timeout: 500
    });
  });
});
```

## Risk Mitigation

### Potential Risks and Solutions

**Risk 1: User Experience Degradation**
- *Problem*: Users expect to see token counts immediately
- *Solution*: Clear visual indicators and progressive loading
- *Mitigation*: A/B testing with user feedback collection

**Risk 2: Increased Complexity**
- *Problem*: Lazy loading adds state management complexity
- *Solution*: Centralized token counting state with clear APIs
- *Mitigation*: Comprehensive testing and documentation

**Risk 3: Race Conditions**
- *Problem*: Multiple token calculations for same file
- *Solution*: Request deduplication and locking mechanisms
- *Mitigation*: Extensive concurrent testing

**Risk 4: Cache Inconsistency**
- *Problem*: Cached tokens might become stale
- *Solution*: Cache invalidation on file changes
- *Mitigation*: File modification timestamp tracking

### Rollback Strategy

**Gradual Rollout:**
1. **Feature Flag**: Toggle lazy counting on/off
2. **Parallel Implementation**: Keep old system as fallback
3. **Performance Monitoring**: Real-time metrics comparison
4. **User Preference**: Allow users to choose behavior

```typescript
const useLazyTokenCounting = () => {
  const isEnabled = useFeatureFlag('lazy-token-counting');
  const userPreference = useUserPreference('token-counting-mode');
  
  return isEnabled && userPreference !== 'eager';
};
```

## Success Criteria

### Technical Success Metrics

**Must Have:**
- [ ] Initial file tree loads in <2 seconds for 1000+ files
- [ ] Token calculations triggered only on user interaction
- [ ] Memory usage reduced by >50% on initial load
- [ ] Zero performance regression for small workspaces (<100 files)

**Should Have:**
- [ ] Directory aggregation completes in <1 second
- [ ] Cache hit rate >80% for repeated interactions
- [ ] Background prefetching doesn't impact foreground operations
- [ ] Touch device support with appropriate interaction patterns

**Could Have:**
- [ ] Predictive token counting for likely-to-be-selected files
- [ ] Smart prefetching based on user patterns
- [ ] Advanced caching with LRU eviction
- [ ] Performance analytics dashboard

### User Experience Success Metrics

**Qualitative Measures:**
- Users can browse large workspaces without waiting
- Hover interactions feel responsive and intentional
- Token information appears when needed without delay
- No confusion about when/why tokens are calculated

**Quantitative Measures:**
- >95% of users complete workspace loading without timeout
- <1% of users report token counting as "slow"
- >90% user satisfaction with file tree responsiveness
- Zero critical bugs related to token calculation accuracy

## Implementation Timeline

### Week 1: Foundation (Remove Eager Counting)
- **Days 1-2**: Remove token counting from file processing pipeline
- **Days 3-4**: Update main process and IPC handlers
- **Days 5-7**: Testing and initial performance validation

### Week 2: Hover-Based Counting
- **Days 1-3**: Implement `useTokenCountingOnDemand` hook
- **Days 4-5**: Update TreeItem and FileCard components
- **Days 6-7**: UI polish and hover interaction testing

### Week 3: Directory Aggregation
- **Days 1-3**: Build DirectoryTokenAggregator system
- **Days 4-5**: Integrate with tree components
- **Days 6-7**: Performance testing and optimization

### Week 4: Priority System and Polish
- **Days 1-3**: Implement priority-based queue system
- **Days 4-5**: Selection-based immediate counting
- **Days 6-7**: Final testing and performance validation

### Week 5: Testing and Deployment
- **Days 1-3**: Comprehensive testing across all workspace sizes
- **Days 4-5**: User acceptance testing and feedback
- **Days 6-7**: Documentation and deployment preparation

## Conclusion

This lazy/on-demand token counting implementation will transform PasteFlow's performance for large workspaces while maintaining the rich token information users rely on. By moving token calculation out of the critical path and into user-driven interactions, we achieve both performance gains and a more responsive user experience.

The phased approach ensures stable progress with clear milestones, while the comprehensive testing strategy validates both performance improvements and functional correctness. The success metrics provide clear targets for measuring the implementation's effectiveness.

The end result will be a PasteFlow that loads large workspaces instantly while providing token information exactly when and where users need it, creating a significantly improved developer experience for AI-assisted coding workflows.